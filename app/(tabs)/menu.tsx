import { Text } from "@/components/ui/text";
import { View } from "@/components/ui/view";
import { useBottomTabBarHeight } from "@react-navigation/bottom-tabs";
import React from "react";
import { StyleSheet } from "react-native";

export default function MenuScreen() {
  const bottom = useBottomTabBarHeight();

  return (
    <View style={[styles.container, { paddingBottom: bottom }]}>
      {/* Header */}
      <View style={styles.header}>
        <Text variant="title">Menu</Text>
      </View>

      {/* Content */}
      <View style={styles.content}>
        <Text variant="body">Menu options coming soon...</Text>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    paddingTop: 64,
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
    justifyContent: "center",
    alignItems: "center",
  },
});