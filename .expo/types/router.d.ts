/* eslint-disable */
import * as Router from 'expo-router';

export * from 'expo-router';

declare module 'expo-router' {
  export namespace ExpoRouter {
    export interface __routes<T extends string | object = string> {
      hrefInputParams: { pathname: Router.RelativePathString, params?: Router.UnknownInputParams } | { pathname: Router.ExternalPathString, params?: Router.UnknownInputParams } | { pathname: `/_sitemap`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}` | `/`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/members` | `/members`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/menu` | `/menu`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/tasks` | `/tasks`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/transactions` | `/transactions`; params?: Router.UnknownInputParams; } | { pathname: `/+not-found`, params: Router.UnknownInputParams & {  } };
      hrefOutputParams: { pathname: Router.RelativePathString, params?: Router.UnknownOutputParams } | { pathname: Router.ExternalPathString, params?: Router.UnknownOutputParams } | { pathname: `/_sitemap`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tabs)'}` | `/`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tabs)'}/members` | `/members`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tabs)'}/menu` | `/menu`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tabs)'}/tasks` | `/tasks`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tabs)'}/transactions` | `/transactions`; params?: Router.UnknownOutputParams; } | { pathname: `/+not-found`, params: Router.UnknownOutputParams & {  } };
      href: Router.RelativePathString | Router.ExternalPathString | `/_sitemap${`?${string}` | `#${string}` | ''}` | `${'/(tabs)'}${`?${string}` | `#${string}` | ''}` | `/${`?${string}` | `#${string}` | ''}` | `${'/(tabs)'}/members${`?${string}` | `#${string}` | ''}` | `/members${`?${string}` | `#${string}` | ''}` | `${'/(tabs)'}/menu${`?${string}` | `#${string}` | ''}` | `/menu${`?${string}` | `#${string}` | ''}` | `${'/(tabs)'}/tasks${`?${string}` | `#${string}` | ''}` | `/tasks${`?${string}` | `#${string}` | ''}` | `${'/(tabs)'}/transactions${`?${string}` | `#${string}` | ''}` | `/transactions${`?${string}` | `#${string}` | ''}` | { pathname: Router.RelativePathString, params?: Router.UnknownInputParams } | { pathname: Router.ExternalPathString, params?: Router.UnknownInputParams } | { pathname: `/_sitemap`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}` | `/`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/members` | `/members`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/menu` | `/menu`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/tasks` | `/tasks`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/transactions` | `/transactions`; params?: Router.UnknownInputParams; } | `/+not-found${`?${string}` | `#${string}` | ''}` | { pathname: `/+not-found`, params: Router.UnknownInputParams & {  } };
    }
  }
}
