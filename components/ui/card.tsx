import { useThemeColor } from '@/hooks/useThemeColor';
import { CORNERS, BORDER_RADIUS } from '@/theme/globals';
import { forwardRef } from 'react';
import { View, ViewProps, ViewStyle } from 'react-native';

interface CardProps extends ViewProps {
  variant?: 'default' | 'outline';
  lightColor?: string;
  darkColor?: string;
}

export const Card = forwardRef<View, CardProps>(
  ({ variant = 'default', lightColor, darkColor, style, ...props }, ref) => {
    const cardColor = useThemeColor(
      { light: lightColor, dark: darkColor },
      'card'
    );
    const borderColor = useThemeColor({}, 'border');

    const getCardStyle = (): ViewStyle => {
      const baseStyle: ViewStyle = {
        borderRadius: BORDER_RADIUS,
        padding: 16,
      };

      switch (variant) {
        case 'outline':
          return {
            ...baseStyle,
            backgroundColor: 'transparent',
            borderWidth: 1,
            borderColor,
          };
        default:
          return {
            ...baseStyle,
            backgroundColor: cardColor,
            borderWidth: 1,
            borderColor
          };
      }
    };

    return (
      <View ref={ref} style={[getCardStyle(), style]} {...props} />
    );
  }
);

Card.displayName = 'Card';